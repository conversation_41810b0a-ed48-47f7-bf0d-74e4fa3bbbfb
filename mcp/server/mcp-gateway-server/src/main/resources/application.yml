server:
  port: 19000

spring:
  application:
    name: mcp-gateway-server
  ai:
    mcp:
      server:
        name: mcp-gateway-server
        version: 1.0.0
        enabled: true
    dashscope:
      api-key: ${DASHSCOPE_API_KEY}

    alibaba:
      mcp:
        gateway:
          enabled: true
          nacos:
            service-names: mcp-nacos-restful
        nacos:
          namespace: 4ad3108b-4d44-43d0-9634-3c1ac4850c8c
          server-addr: 127.0.0.1:8848
          username: nacos
          password: nacos

# 调试日志
logging:
  level:
    io:
      modelcontextprotocol:
        client: DEBUG
        spec: DEBUG
        server: DEBUG
