server:
  port: 20100

spring:
  application:
    name: mcp-stdio-client
  main:
    web-application-type: none
  ai:
    openai:
      api-key: ${DASHSCOPE_API_KEY}
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      chat:
        options:
          model: qwen-max
    mcp:
      client:
        enabled: true
        name: my-mcp-client
        version: 1.0.0
        request-timeout: 30s
        type: SYNC  # or ASYNC for reactive applications
        stdio:
          servers-configuration: classpath:mcp-servers.json