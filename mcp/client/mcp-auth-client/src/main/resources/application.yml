server:
  port: 19101

spring:
  application:
    name: mcp-auth-client
  main:
    web-application-type: none
  ai:
    openai:
      api-key: ${DASHSCOPE_API_KEY}
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      chat:
        options:
          model: qwen-max
    mcp:
      client:
        enabled: true
        name: my-mcp-client
        version: 1.0.0
        request-timeout: 600s
        type: ASYNC  # or ASYNC for reactive applications
        sse:
          connections:
            server1:
              url: http://localhost:19000 # 本地
              headers:
                token-yingzi-1: yingzi-1
                token-yingzi-2: yingzi-2
