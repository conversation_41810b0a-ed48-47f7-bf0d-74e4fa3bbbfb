server:
  port: 19100

spring:
  application:
    name: mcp-recovery-client
  ai:
    openai:
      api-key: ${DASHSCOPE_API_KEY}
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      chat:
        options:
          model: qwen-max
    mcp:
      client:
        enabled: false
        name: my-mcp-client
        version: 1.0.0
        request-timeout: 600s
        type: ASYNC  # or ASYNC for reactive applications
        sse:
          connections:
            server1:
              url: http://localhost:19000 # 本地

    alibaba:
      mcp:
        recovery:
          enabled: true
          ping: 5s
          delay: 5s
          stop: 10s

# 打印日志
logging:
  level:
    com:
      alibaba:
        cloud:
          ai:
            mcp:
              client: DEBUG