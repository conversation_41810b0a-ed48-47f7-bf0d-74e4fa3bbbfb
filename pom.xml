<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.spring.ai.tutorial</groupId>
    <artifactId>spring-ai-tutorial</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>chat/alibaba-chat</module>
        <module>chat/openai-chat</module>
        <module>chat/deepseek-chat</module>
        <module>advisor/advisor-base</module>
        <module>tool-calling</module>
        <module>structured-output</module>
        <module>vector/vector-simple</module>
        <module>vector/vector-elasticsearch</module>
        <module>rag/rag-simple</module>
        <module>rag/rag-elasticsearch</module>
        <module>advisor/advisor-memory-mysql</module>
        <module>advisor/advisor-memory-redis</module>
        <module>advisor/advisor-memory-sqlite</module>
        <module>mcp/server/mcp-webflux-server</module>
        <module>mcp/client/mcp-webflux-client</module>
        <module>mcp/server/mcp-stdio-server</module>
        <module>mcp/client/mcp-stdio-client</module>
        <module>vector/vector-redis</module>
        <module>rag/rag-etl-pipeline</module>
        <module>mcp/server/mcp-nacos3-server</module>
        <module>mcp/client/mcp-nacos3-client</module>
        <module>rag/rag-evaluation</module>
        <module>observability</module>
        <module>graph/stream-node</module>
        <module>graph/human-node</module>
        <module>graph/simple</module>
        <module>graph/mcp-node</module>
        <module>graph/parallel-node</module>
        <module>mcp/client/mcp-auth-client</module>
        <module>mcp/server/mcp-auth-server</module>
        <module>other/restful</module>
        <module>other/nacos-swagger-restfult</module>
        <module>mcp/server/mcp-nacos-parse-swagger-server</module>
        <module>mcp/server/mcp-gateway-server</module>
        <module>other/nacos-restful</module>
        <module>mcp/client/mcp-dynamic-tool-client</module>
        <module>mcp/client/mcp-return-direct-client</module>
        <module>graph/observe-langfuse</module>
        <module>mcp/client/mcp-recovery-client</module>
        <module>advisor/advisor-deep-think</module>
        <module>graph/agent/react-agent</module>
        <module>graph/agent/reflection-agent</module>
        <module>graph/parallel-stream-node</module>
        <module>chat/chat-setting</module>
        <module>advisor/advisor-memory-mem0</module>
        <module>vector/vector-pgvector</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- Spring AI -->
        <spring-ai.version>1.0.1</spring-ai.version>

        <!-- Spring Boot -->
        <spring-boot.version>3.4.5</spring-boot.version>
        <es.version>8.17.4</es.version>
        <jedis.version>5.2.0</jedis.version>
        <mysql.version>8.0.32</mysql.version>
        <sqlite.verson>3.49.1.0</sqlite.verson>

        <!-- Spring AI Alibaba -->
        <spring-ai-alibaba.version>1.0.0.3</spring-ai-alibaba.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-bom</artifactId>
                <version>${spring-ai-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
        <repository>
            <id>sonatype</id>
            <name>OSS Sonatype</name>
            <url>https://oss.sonatype.org/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>aliyunmaven</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>